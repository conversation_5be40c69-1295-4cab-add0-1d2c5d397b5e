"""
Agent Creation and Management
memory, observability.
This module provides functions to create different types of trading agents
with various configurations and filtering strategies.
"""

from typing import Dict

from wheel_trader.smolagents_e2b import HealthAwareAgent
# from smolagents.tools import CodeInterpreter
from .tools import (
    aggressive_market_analysis_tool,
    conservative_market_analysis_tool,
    market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
)


def create_trading_agent(name: str = "trading_agent", model=None) -> HealthAwareAgent:
    """
    Create a health-aware trading agent with E2B execution and domain-specific tools.

    Args:
        name: Name for the agent
        model: Model to use (defaults to InferenceClientModel)

    Returns:
        HealthAwareAgent configured for trading tasks
    """
    tools = [
        options_data_tool,
        market_analysis_tool,
        risk_assessment_tool
    ]

    return HealthAwareAgent(
        name=name,
        model=model,
        tools=tools
    )


def create_multi_agent_system(model=None) -> Dict[str, HealthAwareAgent]:
    """
    Create a multi-agent system with specialized agents.

    Returns:
        Dictionary of specialized agents
    """
    return {
        "data_agent": HealthAwareAgent(
            name="data_agent",
            model=model,
            tools=[options_data_tool]
        ),
        "analysis_agent": HealthAwareAgent(
            name="analysis_agent",
            model=model,
            tools=[market_analysis_tool]
        ),
        "risk_agent": HealthAwareAgent(
            name="risk_agent",
            model=model,
            tools=[risk_assessment_tool]
        ),
        "coordinator_agent": HealthAwareAgent(
            name="coordinator_agent",
            model=model,
            tools=[options_data_tool, market_analysis_tool, risk_assessment_tool]
        )
    }


def create_conservative_agent(name: str = "conservative_agent", model=None) -> HealthAwareAgent:
    """
    Create a conservative trading agent that excludes biotech stocks.

    Args:
        name: Name for the agent

    Returns:
        HealthAwareAgent configured with biotech exclusion
    """
    tools = [
        conservative_market_analysis_tool,
        options_data_tool,
        risk_assessment_tool
    ]
    # tools = tools.append(
    #     CodeInterpreter( 
    #         env={
    #             "DATABASE_URL": os.environ.get("DATABASE_URL"),
    #             "MY_SECRET_KEY": os.environ.get("MY_SECRET_KEY"),
    #         },
    #         requirements=[
    #             "psycopg2-binary",
    #         ]
    #     )
    # )

    # For E2B compatibility, we need to handle environment gracefully
    try:
        agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            additional_imports="psycopg2-binary supabase openai",
            health_threshold=0.7,
            memory_backend="pgvector",
            enable_observability=True
        )
    except Exception as e:
        import logging
        logging.warning(f"Creating agent with limited features due to: {e}")
        agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            health_threshold=0.7,
            memory_backend=None,  # Disable memory for E2B
            enable_observability=False  # Disable observability for E2B
        )

    return agent


def create_aggressive_agent(name: str = "aggressive_agent") -> HealthAwareAgent:
    """
    Create an aggressive trading agent that includes all stocks (no biotech exclusion).
    
    Args:
        name: Name for the agent
        
    Returns:
        HealthAwareAgent configured without biotech exclusion
    """
    tools = [
        aggressive_market_analysis_tool,   # No biotech filtering
        options_data_tool,                 # Regular options data  
        risk_assessment_tool              # Regular risk assessment
    ]
    
    # For E2B compatibility, we need to handle environment gracefully
    try:
        agent = HealthAwareAgent(
            name=name,
            tools=tools,
            health_threshold=0.7,
            memory_backend="pgvector",
            enable_observability=True
        )
    except Exception as e:
        # Fallback for E2B environments where external services aren't available
        import logging
        logging.warning(f"Creating agent with limited features due to: {e}")
        agent = HealthAwareAgent(
            name=name,
            tools=tools,
            health_threshold=0.7,
            memory_backend=None,  # Disable memory for E2B
            enable_observability=False  # Disable observability for E2B
        )

    return agent


def create_comparative_agent_pair() -> dict:
    """
    Create both conservative and aggressive agents for comparison.
    
    Returns:
        Dictionary with both agents: {"conservative": agent_a, "aggressive": agent_b}
    """
    return {
        "conservative": create_conservative_agent("conservative_agent"),
        "aggressive": create_aggressive_agent("aggressive_agent")
    }


# Simple test function to verify agents work
def test_agent_filtering():
    """
    Simple test to verify the agents have different filtering behavior.
    """
    print("🧪 Testing Comparative Agents...")
    
    # Create both agents
    agents = create_comparative_agent_pair()
    conservative = agents["conservative"]
    aggressive = agents["aggressive"]
    
    print(f"✅ Created conservative agent: {conservative.name}")
    print(f"✅ Created aggressive agent: {aggressive.name}")
    
    # Test that they have different tools
    conservative_tools = [tool.__name__ for tool in conservative.agent.tools]
    aggressive_tools = [tool.__name__ for tool in aggressive.agent.tools]
    
    print(f"Conservative tools: {conservative_tools}")
    print(f"Aggressive tools: {aggressive_tools}")
    
    print("✅ Comparative agents created successfully!")
    
    return agents


def run_comparative_test(task: str) -> dict:
    """
    Run the same task on both conservative and aggressive agents.

    Args:
        task: The trading task to execute (e.g., "Analyze MRNA for trading opportunities")

    Returns:
        Dictionary with results from both agents
    """
    print(f"🧪 Running Comparative Test: {task}")
    print("=" * 50)

    # Create both agents
    agents = create_comparative_agent_pair()
    conservative = agents["conservative"]
    aggressive = agents["aggressive"]

    results = {}

    # Run task on conservative agent
    print("🔒 Running Conservative Agent...")
    try:
        conservative_result = conservative.run(task)
        results["conservative"] = {
            "success": True,
            "result": conservative_result,
            "agent_name": conservative.name
        }
        print("✅ Conservative completed")
    except Exception as e:
        results["conservative"] = {
            "success": False,
            "error": str(e),
            "agent_name": conservative.name
        }
        print(f"❌ Conservative failed: {e}")

    # Run task on aggressive agent
    print("🚀 Running Aggressive Agent...")
    try:
        aggressive_result = aggressive.run(task)
        results["aggressive"] = {
            "success": True,
            "result": aggressive_result,
            "agent_name": aggressive.name
        }
        print("✅ Aggressive completed")
    except Exception as e:
        results["aggressive"] = {
            "success": False,
            "error": str(e),
            "agent_name": aggressive.name
        }
        print(f"❌ Aggressive failed: {e}")

    return results


def print_comparison_results(results: dict):
    """
    Print a nice comparison of the results from both agents.
    """
    print("\n📊 COMPARISON RESULTS")
    print("=" * 50)

    for agent_type, result in results.items():
        print(f"\n{agent_type.upper()} AGENT:")
        print("-" * 20)

        if result["success"]:
            print("✅ Status: Success")
            print(f"📝 Result: {str(result['result'])[:200]}...")
        else:
            print("❌ Status: Failed")
            print(f"🚨 Error: {result['error']}")


if __name__ == "__main__":
    # Run test if file is executed directly
    test_agent_filtering()
